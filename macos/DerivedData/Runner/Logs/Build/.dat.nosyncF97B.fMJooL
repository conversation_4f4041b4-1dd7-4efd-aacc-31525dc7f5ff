<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>B567CB8D-6AB6-44B8-B4A9-F2A72DD92818</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>B567CB8D-6AB6-44B8-B4A9-F2A72DD92818.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>Runner project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>Flutter Assemble</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Cleaning workspace Runner with scheme Flutter Assemble</string>
			<key>timeStartedRecording</key>
			<real>773520388.60670805</real>
			<key>timeStoppedRecording</key>
			<real>773520389.27031803</real>
			<key>title</key>
			<string>Cleaning workspace Runner with scheme Flutter Assemble</string>
			<key>uniqueIdentifier</key>
			<string>B567CB8D-6AB6-44B8-B4A9-F2A72DD92818</string>
		</dict>
	</dict>
</dict>
</plist>
