# gogetit - Online Shopping Flutter App with Python & PHP Integration

**ชื่อโปรเจกต์:** gogetit - แอปขายของออนไลน์ พร้อมระบบจัดการอีเมลและจ่ายเงิน

**คำอธิบาย:**  
แอปนี้พัฒนาโดยใช้ Flutter สำหรับการแสดงสินค้า เพิ่มลงตะกร้า และชำระเงิน พร้อมฟังก์ชันพิเศษ:
- ระบบตรวจสอบ Gmail โดยใช้ Python
- ระบบล้างข้อมูลชำระเงินผ่าน PHP

**การ RUN  โปรเจกต์ทั้งหมดทางเรา มีตัวรันให้:**
1.กดใช้งาน run_all.bat โปรเจกต์ทั้งหมดที่ทำจะ RUN ให้ทันที
- ก่อนจะรันติดตั้งโปรแกรมทั้งหมดก่อน
- เมื่อติดตั้งโปรแกรมเรียบร้อยเเล้ว แตกไฟล์ไว้ที่ ไดฟ์ C (เท่านั้น) ถ้าไม่ได้แตกไฟล์ จะไม่สามารถใช้งานrun_all.bat
- C:\gogetit\lib\run_all.bat

**วิธีการใช้งานใช้งานระบบชำระเงิน:**
1.เมื่อรันโปรเจ็คทั้งหมดเเล้ว หรือ รัน run_all.bat แล้วและเข้าไปถึงหน้าชำระเงินเเล้ว (ธนาคารที่สแกนจ่ายเงินได้ กรุงเทพ กรุงไทย ออมสิน)
- ส่ง อีเมล ไปที่ <EMAIL>
- มีหัวข้อเรื่องว่า บริการแจ้งเตือนรายการเงินเข้าบัญชีจากพร้อมเพย์ หรือ 977-4-xxx828 เป็น KEYWORDS เพื่อการทดสอบการชำระเงิน
- เมื่อส่งไปเเล้วระบบจะแจ้งเตือนที่หน้านั้น ว่าชำระเงินเรียบร้อยเเล้ว เป็นอันเสร็จการทดสอบ
2.ถ้าไม่อยากส่งอีกเมลเข้าไปในไฟล์ 
- C:\gogetit\lib\web\assets\payment_status.txt เเละ พิม paid เเละ Save ระบบจะเเจ้งเตือนว่าชำระเงินเรียบร้อยเเล้ว 

**ฟีเจอร์หลัก:**
- หน้าหลัก (Home): แสดงรายการสินค้า
- รายการโปรด (Saved): กดถูกใจสินค้า
- ตะกร้า (Cart): รวมสินค้าเพื่อชำระเงิน
- หน้ารอโหลด (SplashScreen)
- รายละเอียดสินค้า
- ระบบเช็ค Gmail (gmail_checker.py)
- ระบบจัดการข้อมูลชำระเงิน (clear_payment.php)


**วิธีใช้งานแอป Flutter:**
1. ติดตั้ง Flutter SDK และ Dart SDK
2. เปิดใน VS Code / Android Studio
3. ติดตั้ง dependency:
   ```bash
   flutter pub get
   flutter run

**วิธีใช้งานแอป PHP สามารถติดตั้ง โปรแกรม XAMPP ได้เลย:**
1.วิธีการรัน PHP ตรงๆ เพื่อใช้งานระบบล้างข้อมูลการชำระเงิน 
  เข้าไปที่ไฟล์ C:\gogetit\lib\web\assets และ คลิกที่ช่องบนและพิม CMD
   ``bash
  คำสั่งรัน php -S 127.0.0.1:8080

**วิธีใช้งานแอป Python :**
1.ไปที่เว็บไซต์: https://www.python.org/downloads/
2.ดาวน์โหลด Python (แนะนำ Python 3.10+)
3.จากนั้นกด "Install Now"
4.ตรวจสอบว่า Python ติดตั้งสำเร็จ: 
 ``bash
 python --version
5.วิธีรัน python ตรงๆเพื่อใช้งานระบบตรวจสอบ Gmail การชำระเงิน
   เข้าไปที่ไฟล์ C:\gogetit\lib\gmail_checker\gmail_checker.py และ คลิกที่ช่องบนและพิม CMD 
      ``bash
      gmail_checker.py0

**สรุปวิธี RUN ง่านที่สุด ทางเรา มีตัวรันให้ กดใช้งาน run_all.bat โปรเจกต์ทั้งหมดที่ทำจะ RUN ให้ทันที:**
**ก่อนจะรันติดตั้งโปรแกรมทั้งหมดก่อน:**