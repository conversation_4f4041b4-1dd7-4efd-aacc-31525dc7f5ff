import '../models/product.dart';

class ProductRepository {
  static final ProductRepository _instance = ProductRepository._internal();
  factory ProductRepository() => _instance;
  ProductRepository._internal();

  final List<Product> allProducts = [];

  void addProduct(Product product) {
    if (!allProducts.any((p) => p.id == product.id)) {
      allProducts.add(product);
    }
  }

  List<Product> get favorites =>
      allProducts.where((p) => p.isFavorite).toList();
}
