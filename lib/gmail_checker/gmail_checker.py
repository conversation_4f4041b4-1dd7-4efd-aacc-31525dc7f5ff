import os
import time
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

KEYWORDS = ["บริการแจ้งเตือนรายการเงินเข้าบัญชีจากพร้อมเพย์", "977-4-xxx828"]
SCOPES = ['https://www.googleapis.com/auth/gmail.readonly']

def load_read_ids(file_path):
    if os.path.exists(file_path):
        with open(file_path, 'r', encoding='utf-8') as f:
            return set(line.strip() for line in f)
    return set()

def save_read_id(file_path, message_id):
    with open(file_path, 'a', encoding='utf-8') as f:
        f.write(message_id + '\n')

def write_payment_status(status_path, status):
    os.makedirs(os.path.dirname(status_path), exist_ok=True)
    with open(status_path, 'w', encoding='utf-8') as f:
        f.write(status)

def check_payment():
    creds = None
    current_dir = os.path.dirname(__file__)
    token_path = os.path.join(current_dir, 'token.json')
    cred_path = os.path.join(current_dir, 'credentials.json')
    read_ids_path = os.path.join(current_dir, 'read_messages.txt')
    status_path = os.path.join(current_dir, '..','web', 'assets', 'payment_status.txt')

    read_ids = load_read_ids(read_ids_path)

    if os.path.exists(token_path):
        creds = Credentials.from_authorized_user_file(token_path, SCOPES)

    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(cred_path, SCOPES)
            creds = flow.run_local_server(port=0)
        with open(token_path, 'w', encoding='utf-8') as token:
            token.write(creds.to_json())

    service = build('gmail', 'v1', credentials=creds)
    results = service.users().messages().list(userId='me', maxResults=5, labelIds=['INBOX']).execute()
    messages = results.get('messages', [])

    for msg in messages:
        msg_id = msg['id']
        if msg_id in read_ids:
            continue

        msg_data = service.users().messages().get(userId='me', id=msg_id).execute()
        headers = msg_data.get('payload', {}).get('headers', [])

        for header in headers:
            if header.get('name') == 'Subject':
                subject = header.get('value', '').lower()
                save_read_id(read_ids_path, msg_id)

                if any(keyword in subject for keyword in KEYWORDS):
                    print("✅ ตรวจพบอีเมลเกี่ยวกับการชำระเงิน:", subject)

                    # ✅ เขียนสถานะลงไฟล์ครั้งเดียว
                    write_payment_status(status_path, 'paid')

                    return True

    return False

if __name__ == '__main__':
    print("🔄 กำลังตรวจสอบอีเมลใหม่ทุก ๆ 10 วินาที... (กด Ctrl+C เพื่อหยุด)")
    try:
        while True:
            check_payment()
            time.sleep(10)
    except KeyboardInterrupt:
        print("\n🛑 หยุดการตรวจสอบ")
