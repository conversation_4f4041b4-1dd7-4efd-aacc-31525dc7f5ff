class PromptPayQr {
  static String generateQrData({
    required String phoneNumber,
    double? amount,
  }) {
    String id = phoneNumber;
    if (id.startsWith('0')) {
      id = '66${id.substring(1)}';
    }

    final payload = _buildPayload(id, amount);
    final checksum = _crc16ccitt(payload + '6304');
    return payload + '6304' + checksum;
  }

  static String _buildPayload(String id, double? amount) {
    String payload = '';
    payload += _format('00', '01'); // Format Indicator
    payload += _format('01', '11'); // Dynamic QR

    final merchantAccount = _format('00', 'A000000677010111') + _format('01', id);
    payload += _format('29', merchantAccount); // Merchant Info

    payload += _format('52', '0000');
    payload += _format('53', '764');

    if (amount != null) {
      payload += _format('54', amount.toStringAsFixed(2));
    }

    payload += _format('58', 'TH');
    payload += _format('59', 'PromptPay'); // ตรงกันกับ QR ที่เฮียแนบ
    payload += _format('60', 'Bangkok');

    return payload;
  }

  static String _format(String id, String value) =>
      '$id${value.length.toString().padLeft(2, '0')}$value';

  static String _crc16ccitt(String input) {
    List<int> bytes = input.codeUnits;
    int crc = 0xFFFF;
    for (final b in bytes) {
      crc ^= (b << 8);
      for (int i = 0; i < 8; i++) {
        crc = (crc & 0x8000) != 0 ? (crc << 1) ^ 0x1021 : (crc << 1);
      }
      crc &= 0xFFFF;
    }
    return crc.toRadixString(16).toUpperCase().padLeft(4, '0');
  }
}
