import '../models/product.dart';

class CartService {
  static final CartService _instance = CartService._internal();
  factory CartService() => _instance;
  CartService._internal();

  final List<Product> _cartItems = [];

  List<Product> get cartItems => _cartItems;

  void addToCart(Product product) {
    final index = _cartItems.indexWhere((p) => p.id == product.id);
    if (index == -1) {
      _cartItems.add(product.copyWith(quantity: 1));
    } else {
      _cartItems[index].quantity += 1;
    }
  }

  void removeFromCart(Product product) {
    _cartItems.removeWhere((p) => p.id == product.id);
  }

  void updateQuantity(int index, int delta) {
    _cartItems[index].quantity += delta;
    if (_cartItems[index].quantity < 1) _cartItems[index].quantity = 1;
  }

  double get totalPrice =>
      _cartItems.fold(0, (sum, item) => sum + item.price * item.quantity);

  bool isInCart(int productId) {
    return _cartItems.any((p) => p.id == productId);
  }

  void toggleFavorite(int productId, bool isFav) {
    final index = _cartItems.indexWhere((p) => p.id == productId);
    if (index != -1) {
      _cartItems[index] = _cartItems[index].copyWith(isFavorite: isFav);
    }
  }

  List<Product> getFavorites() {
    return _cartItems.where((p) => p.isFavorite).toList();
  }
  
}
