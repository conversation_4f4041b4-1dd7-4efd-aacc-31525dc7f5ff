import '../models/product.dart';

class ProductService {
  static final ProductService _instance = ProductService._internal();
  factory ProductService() => _instance;
  ProductService._internal();

  final List<Product> _allProducts = [
    Product(id: 1, name: "<PERSON><PERSON> (ใหม่)", imageUrl: "https://...", price: 250, isFavorite: false, quantity: 1),
    Product(id: 2, name: "iPhone 12 Pro Black Edition (มือถือ)", imageUrl: "https://...", price: 1200, isFavorite: false, quantity: 1),
    
    // เพิ่มสินค้าอื่น ๆ
  ];

  List<Product> get allProducts => _allProducts;

  void toggleFavorite(int id) {
    final index = _allProducts.indexWhere((p) => p.id == id);
    if (index != -1) {
      _allProducts[index].isFavorite = !_allProducts[index].isFavorite;
    }
  }

  List<Product> getFavorites() => _allProducts.where((p) => p.isFavorite).toList();
}
