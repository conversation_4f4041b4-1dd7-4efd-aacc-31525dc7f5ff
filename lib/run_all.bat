@echo off
title 🚀 STARTING: PHP + PY + FLUTTER

REM ====== รัน Python Gmail Checker ======
echo [PYTHON] รัน gmail_checker.py...
start "Python Gmail Checker" cmd /k python C:\gogetit\lib\gmail_checker\gmail_checker.py

REM ====== รัน PHP Server (ชี้ไปยัง assets/) ======
echo [PHP] รัน PHP Server ที่พอร์ต 8080...
start "PHP Server" cmd /k php -S 127.0.0.1:8080 -t C:\gogetit\lib\web\assets

REM ====== เปิดเว็บเบราว์เซอร์อัตโนมัติ ======
timeout /t 2 >nul
start "" http://localhost:8080/clear_payment.php

REM ==== รัน Flutter Web ====
echo [FLUTTER] Starting Flutter Web App...
start "Flutter Web" cmd /k cd /d C:\Users\<USER>\Desktop\shopapp\flutterapp && flutter run -d chrome


echo ========================================
echo ✅ ทั้งหมดกำลังทำงานอยู่ในหน้าต่างแยก
echo ❌ ปิดแต่ละหน้าต่างเพื่อหยุดโปรเซส
pause
