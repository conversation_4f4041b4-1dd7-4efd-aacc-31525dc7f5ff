import 'package:flutter/material.dart';
import 'pages/home_page.dart';
import 'pages/saved_page.dart';
import 'pages/cart_page.dart';
import 'models/product.dart';
import 'pages/splash_screen.dart';
import 'package:google_fonts/google_fonts.dart';
void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        textTheme: GoogleFonts.notoSansThaiTextTheme(
          Theme.of(context).textTheme,
        ),
      ),
      home: SplashScreen(),
    );
  }
}

class OnlineShoppingApp extends StatefulWidget {
  const OnlineShoppingApp({super.key});

  @override
  State<OnlineShoppingApp> createState() => _OnlineShoppingAppState();
}

class _OnlineShoppingAppState extends State<OnlineShoppingApp>
    with SingleTickerProviderStateMixin {
  int _selectedIndex = 0;
  late AnimationController _controller;
  late Animation<double> _animation;
  List<Product> savedItems = [];

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(parent: _controller, curve: Curves.easeInOut);
  }

  void toggleFavorite(Product product) {
    setState(() {
      product.isFavorite = !product.isFavorite;
      if (product.isFavorite) {
        savedItems.add(product);
      } else {
        savedItems.removeWhere((item) => item.id == product.id);
      }
    });
  }

  void onTabTapped(int index) {
    _controller.forward(from: 0);
    setState(() => _selectedIndex = index);
  }

  @override
  Widget build(BuildContext context) {
    final List<Widget> pages = [
      HomePage(savedItems: savedItems, onToggleFavorite: toggleFavorite),
      SavedPage(savedItems: savedItems, onToggleFavorite: toggleFavorite),
      const CartPage(),
    ];

    return Scaffold(
      backgroundColor: Colors.white,
      body: pages[_selectedIndex],
      bottomNavigationBar: CustomAnimatedBottomNavBar(
        currentIndex: _selectedIndex,
        onTap: onTabTapped,
        animation: _animation,
      ),
    );
  }
}

class CustomAnimatedBottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final Animation<double> animation;

  const CustomAnimatedBottomNavBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.animation,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(40),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(3, (index) {
          final isSelected = index == currentIndex;
          return GestureDetector(
            onTap: () => onTap(index),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: isSelected ? const Color(0xFFF0F0F0) : Colors.transparent,
                shape: BoxShape.circle,
              ),
              child: ScaleTransition(
                scale: isSelected ? Tween(begin: 0.9, end: 1.2).animate(animation) : AlwaysStoppedAnimation(1.0),
                child: Icon(
                  _getIcon(index),
                  color: isSelected ? const Color(0xFFFF6600) : Colors.black87,
                  size: 26,
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  IconData _getIcon(int index) {
    switch (index) {
      case 0:
        return Icons.home_rounded;
      case 1:
        return Icons.favorite_rounded;
      case 2:
        return Icons.shopping_cart_rounded;
      default:
        return Icons.circle;
    }
  }
}
