import 'package:flutter/material.dart';
import '../models/product.dart';
import '../main.dart'; // ต้องสร้างแยกไฟล์ด้วย

class OnboardingScreen extends StatefulWidget {
  final List<Product> savedItems;
  final Function(Product) onToggleFavorite;

  const OnboardingScreen({
    super.key,
    required this.savedItems,
    required this.onToggleFavorite,
  });

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _controller = PageController();
  int _pageIndex = 0;

  List<Map<String, dynamic>> onboardData = [
    {
      "title": "Discover",
      "subtitle": "Exclusive Products",
      "desc": "Shop the latest trends and premium items all in one place.",
      "icon": Icons.shopping_bag_rounded, // 🛍️
    },
    {
      "title": "Fast & Secure",
      "subtitle": "Checkout",
      "desc": "Enjoy smooth, secure payments with just a few taps.",
      "icon": Icons.credit_card_rounded, // 💳
    },
    {
      "title": "Track Orders",
      "subtitle": "In Real-Time",
      "desc": "Stay updated with your order every step of the way.",
      "icon": Icons.local_shipping_rounded, // 📦
    },
  ];



  void _nextPage() {
    if (_pageIndex == onboardData.length - 1) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (_) => OnlineShoppingApp(), // ✅ เรียกเข้าหน้าแอปหลัก
        ),
      );
    } else {
      _controller.nextPage(duration: const Duration(milliseconds: 300), curve: Curves.ease);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView.builder(
        controller: _controller,
        onPageChanged: (index) => setState(() => _pageIndex = index),
        itemCount: onboardData.length,
        itemBuilder: (context, index) => Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                onboardData[index]["icon"],
                size: 120,
                color: Colors.orange,
              ),
              SizedBox(height: 30),
              Text(onboardData[index]["title"], style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
              Text(onboardData[index]["subtitle"], style: TextStyle(fontSize: 20, color: Colors.orange)),
              SizedBox(height: 10),
              Text(onboardData[index]["desc"], textAlign: TextAlign.center),
              SizedBox(height: 30),
              ElevatedButton(
                onPressed: _nextPage,
                style: ElevatedButton.styleFrom(
                  shape: CircleBorder(),
                  backgroundColor: Colors.orange,
                  padding: EdgeInsets.all(20),
                ),
                child: Icon(Icons.arrow_forward_ios, color: Colors.white),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
