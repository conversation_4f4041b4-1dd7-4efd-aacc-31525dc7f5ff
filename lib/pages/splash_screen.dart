import 'package:flutter/material.dart';
import 'onboarding_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 3), () {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (_) => OnboardingScreen(
            savedItems: [],
            onToggleFavorite: (product) {},
          ),
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromARGB(70, 233, 212, 169),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.network(
              'https://i.postimg.cc/R003tRXV/logo2.png', // <--- ลิงก์จริงใส่ตรงนี้
              width: 300,
              height: 300,
            ),
            const SizedBox(height: 20),
            // const Text(
            //   "GOGETIT",
            //   style: TextStyle(color: Colors.white, fontSize: 28),
            // ),
          ],
        ),
      ),
    );
  }
}
