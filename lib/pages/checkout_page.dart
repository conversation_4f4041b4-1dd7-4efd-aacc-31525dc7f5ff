import 'dart:async';
import 'dart:html' as html;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:qr_flutter/qr_flutter.dart';
import '../utils/promptpay.dart';

class CheckoutPage extends StatefulWidget {
  final double totalPrice;
  const CheckoutPage({super.key, required this.totalPrice});

  @override
  State<CheckoutPage> createState() => _CheckoutPageState();
}

class _CheckoutPageState extends State<CheckoutPage> {
  Timer? _timer;
  Timer? _countdownTimer;
  Duration remaining = const Duration(minutes: 3);

  @override
  void initState() {
    super.initState();
    _clearPaymentStatus();
    _startStatusMonitor();
    _startCountdown();
  }

  void _clearPaymentStatus() async {
    try {
      await html.HttpRequest.request(
        'http://localhost:8080/clear_payment.php',
        method: 'POST',
      );
      print("📨 ล้างสถานะแล้ว");
    } catch (e) {
      print("❌ ล้างสถานะไม่สำเร็จ: $e");
    }
  }

  void _startStatusMonitor() {
    _timer = Timer.periodic(const Duration(seconds: 2), (_) async {
      try {
        final response = await html.HttpRequest.getString(
          'lib/web/assets/payment_status.txt?t=${DateTime.now().millisecondsSinceEpoch}',
        );
        if (response.trim() == 'paid') {
          _timer?.cancel();
          _countdownTimer?.cancel();
          _showPaymentSuccess();
        }
      } catch (_) {}
    });
  }

  void _startCountdown() {
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      setState(() {
        remaining -= const Duration(seconds: 1);
      });
      if (remaining.inSeconds <= 0) {
        _timer?.cancel();
        _countdownTimer?.cancel();
        Navigator.of(context).popUntil((r) => r.isFirst);
      }
    });
  }

void _showPaymentSuccess() {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (_) => Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // ✅ ไอคอนวงกลม + ติ๊กถูก
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Color(0xFF4CAF50), // เขียว
              ),
              child: const Icon(Icons.check, size: 40, color: Colors.white),
            ),
            const SizedBox(height: 24),

            // ✅ ข้อความหลัก
            const Text(
              'ชำระเงินสำเร็จ',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: 12),

            // ✅ จำนวนเงิน
            Text(
              '฿${widget.totalPrice.toStringAsFixed(2)}',
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: 12),

            // ✅ คำอธิบาย
            const Text(
              'ระบบได้ยืนยันยอดเงินแล้ว\nคุณสามารถดำเนินการต่อได้',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14, color: Colors.black54),
            ),

            const SizedBox(height: 24),

            // ✅ ปุ่ม DONE
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFF6600),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onPressed: () {
                  Navigator.of(context).pop(); // ปิด dialog
                  Navigator.of(context).popUntil((r) => r.isFirst); // กลับหน้าหลัก
                },
                child: const Text(
                  'เสร็จสิ้น',
                  style: TextStyle(fontSize: 16,color: Color.fromARGB(255, 255, 255, 255)),
                ),
              ),
            ),
          ],
        ),
      ),
    ),
  );
}


  @override
  void dispose() {
    _timer?.cancel();
    _countdownTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final qrData = PromptPayQr.generateQrData(
      phoneNumber: '0647876935',
      amount: widget.totalPrice,
    );
    final now = DateTime.now().add(remaining);
    final dateStr = DateFormat('dd/MM/yyyy HH:mm').format(now);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        title: const Text('PromptPay', style: TextStyle(color: Colors.black)),
        centerTitle: true,
        leading: const BackButton(color: Colors.black),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFFFF6600), Color(0xFFFF3300)],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Text('GOGETIT',
                    style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
              ),
            ),
            const SizedBox(height: 8),
            const Text('โปรดจับภาพหน้าจอเพื่อนำไปใช้ QR code'),
            const SizedBox(height: 8),
            Image.network(
              'https://i.postimg.cc/FRdkPWbD/Untitled-2.png',
              height: 70,
            ),
            const SizedBox(height: 8),
            QrImageView(
              data: qrData,
              version: QrVersions.auto,
              size: 200,
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
              onPressed: () {
              },
              child: const Text(
                'บันทึกรหัส QR',
                style: TextStyle(color: Colors.white),
              ),
            ),
            const SizedBox(height: 6),
            Text('กรุณาแสกน QR code ก่อน $dateStr ไม่งั้น QR code จะหมดอายุ'),

            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('Price:', style: TextStyle(fontSize: 16)),
                Text('\$${widget.totalPrice.toStringAsFixed(2)}',
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text('คู่มือการชำระ:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
// 🔁 คู่มือการชำระเงิน (แบบใช้ Icons)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: const [
                _GuideIconStep(
                  icon: Icons.camera_alt_outlined,
                  label: 'จับหน้าจอ\nและบันทึก',
                ),
                _ArrowIcon(),
                _GuideIconStep(
                  icon: Icons.account_balance_wallet_outlined,
                  label: 'เปิดแอป\nธนาคาร',
                ),
                _ArrowIcon(),
                _GuideIconStep(
                  icon: Icons.qr_code_scanner_outlined,
                  label: 'เปิดช่อง\nสแกนภาพ',
                ),
                _ArrowIcon(),
                _GuideIconStep(
                  icon: Icons.check_circle_outline,
                  label: 'ชำระเงิน\nเสร็จสิ้น',
                ),
              ],
            ),



            const SizedBox(height: 20),
            OutlinedButton(
              onPressed: () {
                Navigator.of(context).popUntil((r) => r.isFirst);
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.deepOrange,
                side: const BorderSide(color: Colors.deepOrange),
              ),
              child: const Text('กลับไปร้านค้า'),
            ),
          ],
        ),
      ),
    );
  }
}

class _GuideIconStep extends StatelessWidget {
  final IconData icon;
  final String label;

  const _GuideIconStep({required this.icon, required this.label});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Icon(icon, color: Colors.orange, size: 30),
        const SizedBox(height: 4),
        Text(
          label,
          textAlign: TextAlign.center,
          style: const TextStyle(fontSize: 11),
        ),
      ],
    );
  }
}


class _ArrowIcon extends StatelessWidget {
  const _ArrowIcon();

  @override
  Widget build(BuildContext context) {
    return const Icon(Icons.arrow_forward_ios_rounded, size: 12, color: Colors.orange);
  }
}
