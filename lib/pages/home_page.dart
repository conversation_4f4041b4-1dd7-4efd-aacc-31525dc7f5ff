import 'package:flutter/material.dart';
import '../models/product.dart';
import 'product_detail_page.dart';
import 'saved_page.dart';
import 'responsive_banner_slider.dart'; 

class HomePage extends StatefulWidget {
  final List<Product> savedItems;
  final Function(Product) onToggleFavorite;

  const HomePage({
    super.key,
    required this.savedItems,
    required this.onToggleFavorite,
  });

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final TextEditingController _searchController = TextEditingController();
  String searchQuery = "";

  final List<String> categories = ["ทั้งหมด", "เครื่องเกม", "มือถือ", "เครื่องใช้ไฟฟ้า", "ใหม่"];
  String selectedCategory = "ทั้งหมด";

  final List<Product> products = [
    Product(id: 1, name: '<PERSON><PERSON> White (ใหม่)', imageUrl: 'https://i.postimg.cc/5tBH7Ypr/photo-1608231387042-66d1773070a5.png', price: 250.00),
    Product(id: 2, name: 'iPhone 12 Pro Black Edition (มือถือ)', imageUrl: 'https://i.postimg.cc/MpZMtdpB/IMG-6299.webp', price: 1200.00),
    Product(id: 3, name: 'Nintendo Switch 2021 (เครื่องเกม)', imageUrl: 'https://i.postimg.cc/85LMxsQf/56-EE3-E52-F7-EF-43-EC-9-DB1-793340170021.webp', price: 599.00),
    Product(id: 4, name: 'Black + Decker (เครื่องใช้ไฟฟ้า)', imageUrl: 'https://i.postimg.cc/tC45TCZK/photo-1608231387042-66d1773070a51.png', price: 149.00),
    Product(id: 5, name: 'White Neat Mug (ใหม่)', imageUrl: 'https://i.postimg.cc/Yq5XjB6M/photo-1608231387042-66d1773w070a5.png', price: 35.00),
    Product(id: 6, name: 'SMEG Oven - Winter Collection (เครื่องใช้ไฟฟ้า)', imageUrl: 'https://images.unsplash.com/photo-1586208958839-06c17cacdf08?fit=crop&w=300&q=80', price: 8299.00),
    Product(id: 7, name: 'Black Table Fan with Pink Moody Cat (เครื่องใช้ไ ฟฟ้า)', imageUrl: 'https://images.unsplash.com/photo-1618941716939-553df3c6c278?fit=crop&w=300&q=80', price: 79.00),
  ];

  List<Product> filteredProducts() {
    return products.where((p) {
      final matchName = p.name.toLowerCase().contains(searchQuery.toLowerCase());
      return selectedCategory == "ทั้งหมด"
          ? matchName
          : matchName && p.name.toLowerCase().contains(selectedCategory.toLowerCase());
    }).toList();
  }

  Future<void> goToSavedPage() async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => SavedPage(
          savedItems: widget.savedItems,
          onToggleFavorite: widget.onToggleFavorite,
        ),
      ),
    );
    setState(() {});
  }

  void goToDetail(Product product) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => ProductDetailPage(
          product: product,
          savedItems: widget.savedItems,
          onToggleFavorite: widget.onToggleFavorite,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: const Color(0xFFFF6600),
          elevation: 0,
          centerTitle: true,
          title: const Text(
            'HOME PAGE',
            style: TextStyle(color: Color(0xFFFFFFFF), fontWeight: FontWeight.bold),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.favorite_outline, color: Color(0xFFFFFFFF)),
              onPressed: () => goToSavedPage(),
            )
          ],
        ),
        resizeToAvoidBottomInset: false,
        body: Column(
          children: [
Container(
  width: double.infinity,
  color: const Color(0xFFFF6600),
  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
  child: Row(
    children: [
      Expanded(
        child: Container(
          height: 42,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
          ),
          child: Row(
            children: [
              const Icon(Icons.search, color: Colors.orange, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: TextField(
                  controller: _searchController,
                  onChanged: (val) => setState(() => searchQuery = val),
                  style: const TextStyle(fontSize: 14),
                  decoration: const InputDecoration(
                    hintText: 'Search',
                    hintStyle: TextStyle(color: Colors.grey),
                    border: InputBorder.none,
                    isDense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      const SizedBox(width: 6),
      Container(
        height: 42,
        width: 42,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24),
        ),
        child: IconButton(
          padding: EdgeInsets.zero,
          icon: const Icon(Icons.filter_list, color: Color(0xFFFF6600), size: 20),
          onPressed: () {},
        ),
      ),
    ],
  ),
),

            const SizedBox(width: 12),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    const ResponsiveBannerSlider(),
                    const SizedBox(height: 16),
                    SizedBox(
                      height: 36,
                      child: ListView.separated(
                        scrollDirection: Axis.horizontal,
                        itemCount: categories.length,
                        separatorBuilder: (_, __) => const SizedBox(width: 12),
                        itemBuilder: (context, i) {
                          final isSelected = selectedCategory == categories[i];
                          return GestureDetector(
                            onTap: () => setState(() => selectedCategory = categories[i]),
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 16),
                              decoration: BoxDecoration(
                                color: isSelected ? const Color(0xFFFF6600) : Colors.grey[300],
                                borderRadius: BorderRadius.circular(20),
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                categories[i],
                                style: TextStyle(color: isSelected ? Colors.white : Colors.black),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                    LayoutBuilder(
                      builder: (context, constraints) {
                        double itemWidth = (constraints.maxWidth - 12) / 2;
                        double imageHeight = itemWidth;

                        return GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: filteredProducts().length,
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            mainAxisSpacing: 12,
                            crossAxisSpacing: 12,
                            childAspectRatio: 0.68,
                          ),
                          itemBuilder: (context, index) {
                            final item = filteredProducts()[index];
                            final isSaved = widget.savedItems.contains(item);

                            return GestureDetector(
                              onTap: () => goToDetail(item),
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Stack(
                                      children: [
                                        ClipRRect(
                                          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                                          child: Image.network(
                                            item.imageUrl,
                                            width: double.infinity,
                                            height: imageHeight,
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                        Positioned(
                                          top: 10,
                                          right: 10,
                                          child: GestureDetector(
                                            onTap: () => widget.onToggleFavorite(item),
                                            child: Container(
                                              padding: const EdgeInsets.all(6),
                                              decoration: const BoxDecoration(
                                                color: Colors.white,
                                                shape: BoxShape.circle,
                                              ),
                                              child: AnimatedSwitcher(
                                                duration: const Duration(milliseconds: 250),
                                                transitionBuilder: (child, animation) =>
                                                    ScaleTransition(scale: animation, child: child),
                                                child: Icon(
                                                  isSaved ? Icons.favorite : Icons.favorite_border,
                                                  key: ValueKey<bool>(isSaved),
                                                  color: isSaved ? Colors.red : Colors.grey,
                                                  size: 18,
                                                ),
                                              ),
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 10),
                                      child: Text(
                                        item.name,
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 10),
                                      child: Text(
                                        "\$${item.price.toStringAsFixed(2)}",
                                        style: const TextStyle(fontSize: 13, color: Colors.black87),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        );
                      },
                    )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
