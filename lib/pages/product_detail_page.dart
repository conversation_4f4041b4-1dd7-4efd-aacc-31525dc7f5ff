import 'package:flutter/material.dart';
import '../models/product.dart';
import '../services/cart_service.dart';

class ProductDetailPage extends StatefulWidget {
  final Product product;
  final List<Product> savedItems;
  final Function(Product) onToggleFavorite;

  const ProductDetailPage({
    super.key,
    required this.product,
    required this.savedItems,
    required this.onToggleFavorite,
  });

  @override
  State<ProductDetailPage> createState() => _ProductDetailPageState();
}

class _ProductDetailPageState extends State<ProductDetailPage> {
  final cartService = CartService();

  void toggleFavorite() {
    final wasFavorite = widget.savedItems.contains(widget.product); // สถานะก่อนเปลี่ยน

    widget.onToggleFavorite(widget.product); // toggle สถานะ
    setState(() {}); // refresh หน้า

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          wasFavorite
              ? 'นำ ${widget.product.name} ออกจากรายการโปรด'
              : 'เพิ่ม ${widget.product.name} ไปยังรายการโปรด',
        ),
        duration: const Duration(seconds: 1),
      ),
    );
  }


  void addToCart() {
    cartService.addToCart(widget.product);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('เพิ่ม ${widget.product.name} เข้าตะกร้าเรียบร้อย'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isFavorite = widget.savedItems.contains(widget.product);

    return Scaffold(
      backgroundColor: const Color(0xFFF5F6FA),
      body: SafeArea(
        child: Column(
          children: [
            // ส่วนรูปด้านบนเหมือนเดิม
            Stack(
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(32),
                    bottomRight: Radius.circular(32),
                  ),
                  child: Container(
                    height: 300,
                    width: double.infinity,
                    color: Colors.white,
                    child: Image.network(widget.product.imageUrl, fit: BoxFit.contain),
                  ),
                ),
                Positioned(
                  top: 16,
                  left: 16,
                  child: IconButton(
                    icon: const Icon(Icons.arrow_back, size: 28),
                    onPressed: () => Navigator.pop(context),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 18),
            Expanded(
              child: SingleChildScrollView(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.vertical(top: Radius.circular(32)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: const [
                          Icon(Icons.shopping_cart_outlined, color: Color(0xFFFF6600), size: 22),
                          SizedBox(width: 6),
                          Text(
                            "Shopping",
                            style: TextStyle(
                              fontSize: 14,
                              color: Color(0xFFFF6600),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Text(
                              widget.product.name,
                              style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                            ),
                          ),
                          IconButton(
                            icon: Icon(
                              isFavorite ? Icons.favorite : Icons.favorite_border,
                              color: isFavorite ? Colors.red : Colors.grey,
                            ),
                            onPressed: toggleFavorite,
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        "Price Inclusive Off All Taxes",
                        style: TextStyle(color: Colors.grey),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: const [
                          Icon(Icons.location_on_outlined, size: 20, color: Colors.grey),
                          SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              "[บ้านเลขที่ 65/5 หมู่ 7 ต.ท่าช่าง อ.เมือง จ.จันทบุรี 22000]",
                              style: TextStyle(color: Colors.black87),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "\$${widget.product.price}",
                            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                          ),
                          const Text("", style: TextStyle(color: Colors.grey)),
                        ],
                      ),
                      const SizedBox(height: 32),
                      ElevatedButton(
                        onPressed: addToCart,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFFF6600),
                          minimumSize: const Size(double.infinity, 55),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(24),
                          ),
                        ),
                        child: const Text(
                          "ADD TO CART",
                          style: TextStyle(
                            fontSize: 16,
                            letterSpacing: 1.2,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

          ],
        ),
      ),
    );
  }
}
