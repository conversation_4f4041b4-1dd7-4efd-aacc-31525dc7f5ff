import 'package:flutter/material.dart';
import '../services/cart_service.dart';
import 'checkout_page.dart';
class CartPage extends StatefulWidget {
  const CartPage({super.key});

  @override
  State<CartPage> createState() => _CartPageState();
}

class _CartPageState extends State<CartPage> {
  final cartService = CartService();

  void updateQuantity(int index, int delta) {
    setState(() {
      cartService.updateQuantity(index, delta);
    });
  }

  void removeItem(int index) {
    setState(() {
      cartService.cartItems.removeAt(index);
    });
  }

  void toggleFavorite(int index) {
    setState(() {
      final product = cartService.cartItems[index];
      cartService.toggleFavorite(product.id, !product.isFavorite);
      product.isFavorite = !product.isFavorite;
    });
  }


  @override
  Widget build(BuildContext context) {
    final cartItems = cartService.cartItems;

    return Scaffold(
      backgroundColor: const Color(0xFFF6F7FB),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        // leading: IconButton(
        //   icon: const Icon(Icons.arrow_back),
        //   onPressed: () {
        //     if (Navigator.canPop(context)) {
        //       Navigator.pop(context);
        //     } else {
        //       Navigator.pushReplacementNamed(context, '/home');
        //     }
        //   },
        //   color: Colors.black,
        // ),
        title: const Text(
          'Cart',
          style: TextStyle(
            color: Color(0xFFFF6600),
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
      ),
      body: cartItems.isEmpty
          ? const Center(
              child: Text("NoCart", style: TextStyle(fontSize: 16)),
            )
          : Column(
              children: [
                Expanded(
                  child: ListView.separated(
                    padding: const EdgeInsets.all(16),
                    itemCount: cartItems.length,
                    separatorBuilder: (_, __) => const SizedBox(height: 12),
                    itemBuilder: (context, index) {
                      final item = cartItems[index];
                      return Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(18),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.1),
                              blurRadius: 6,
                              offset: const Offset(0, 3),
                            )
                          ],
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: Image.network(item.imageUrl, width: 60, height: 60, fit: BoxFit.cover),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(item.name, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14)),
                                  const Text("Women Style", style: TextStyle(color: Colors.grey, fontSize: 12)),
                                  const SizedBox(height: 8),
                                  Text("\$${item.price}", style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                                ],
                              ),
                            ),
                            Column(
                              children: [
                                // IconButton(
                                //   icon: Icon(
                                //     item.isFavorite ? Icons.favorite : Icons.favorite_border,
                                //     color: item.isFavorite ? Colors.pink : Colors.grey,
                                //   ),
                                //   onPressed: () => toggleFavorite(index), 
                                // ),
                                Row(
                                  children: [
                                    IconButton(
                                      onPressed: () => updateQuantity(index, -1),
                                      icon: const Icon(Icons.remove, size: 20),
                                      color: Colors.black54,
                                    ),
                                    Text("${item.quantity}", style: const TextStyle(fontSize: 14)),
                                    IconButton(
                                      onPressed: () => updateQuantity(index, 1),
                                      icon: const Icon(Icons.add, size: 20),
                                      color: Colors.black54,
                                    ),
                                  ],
                                ),
                                IconButton(
                                  onPressed: () => removeItem(index),
                                  icon: const Icon(Icons.delete, color: Colors.redAccent),
                                ),
                              ],
                            )
                          ],
                        ),
                      );
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text("Total", style: TextStyle(color: Colors.grey, fontSize: 16)),
                          Text("\$${cartService.totalPrice.toStringAsFixed(2)}",
                              style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                        ],
                      ),
                      const SizedBox(height: 16),
                        ElevatedButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (_) => CheckoutPage(totalPrice: cartService.totalPrice),
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFFF6600),
                          minimumSize: const Size.fromHeight(48),
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
                        ),
                        child: const Text(
                          "Pay Now",
                          style: TextStyle(fontSize: 16, color: Colors.white, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }
}
