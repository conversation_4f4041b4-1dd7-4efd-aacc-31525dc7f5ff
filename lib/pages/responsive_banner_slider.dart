import 'dart:async';
import 'package:flutter/material.dart';

class ResponsiveBannerSlider extends StatefulWidget {
  const ResponsiveBannerSlider({super.key});

  @override
  State<ResponsiveBannerSlider> createState() => _ResponsiveBannerSliderState();
}

class _ResponsiveBannerSliderState extends State<ResponsiveBannerSlider> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<String> bannerImages = [
    'https://i.postimg.cc/HnwmHvfY/Untitled-14.png',
    'https://i.postimg.cc/j2FHxvN1/Untitled-16.png',
    'https://i.postimg.cc/RFKMg0Cb/Untitled-13.png',
  ];

  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(seconds: 4), (Timer timer) {
      if (_pageController.hasClients) {
        _currentPage = (_currentPage + 1) % bannerImages.length;
        _pageController.animateToPage(
          _currentPage,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _timer?.cancel();
    super.dispose();
  }

@override
Widget build(BuildContext context) {
  double bannerHeight = MediaQuery.of(context).size.width * 0.36; // ปรับขนาด

    return SizedBox(
      height: bannerHeight,
      child: PageView.builder(
        controller: _pageController,
        itemCount: bannerImages.length,
        itemBuilder: (context, index) {
          return ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Image.network(
              bannerImages[index],
              fit: BoxFit.cover,
              width: double.infinity,
            ),
          );
        },
      ),
    );
  }
}
